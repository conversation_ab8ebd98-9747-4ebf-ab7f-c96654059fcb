// /src/pages/Channel/EditChannelFormModal.tsx

import {
    App,
    Button,
    Divider,
    Flex,
    Form,
    Input,
    InputNumber,
    InputRef,
    message,
    Modal,
    Select,
    Space,
    Spin,
    Switch,
    TreeSelect,
    Tooltip,
    Typography,
    Tag
} from "antd";
import {CHANNEL_MODEL_LIST, CHANNEL_TYPE_OPTIONS, modalPropsWithFooter} from "../../constants";
import React, {lazy, useContext, useEffect, useMemo, useRef, useState} from "react";
import {
    API,
    commaSeparatedStringValidationRule,
    jsonValidationRule,
    parseValueToSubmitString,
    safeParseJSON,
    safeStringifyJSON,
    showError
} from "../../helpers";
import { copy } from "../../helpers/utils";
import {StatusContext} from '../../context/Status';
import {UserContext} from '../../context/User';
import {useGroup} from '../../context/Group';

import {
    CheckCircleTwoTone,
    CloseCircleTwoTone,
    EyeInvisibleOutlined,
    EyeOutlined,
    PlusOutlined,
    SettingFilled,
    SettingTwoTone,
    SunFilled,
    SyncOutlined
} from "@ant-design/icons";
import {AxiosResponse} from "axios";
import { useTranslation } from 'react-i18next';  // 添加这行


const {Option} = Select;

interface ChannelType {
    id: number;//渠道ID
    name: string;//渠道名称
    group: string;//可用该渠道的用户分组,"ag,bg,cg"
    key: string;//渠道鉴权密钥
    models: string;//该渠道支持的模型，"am,bm,cm"
    model_mapping: string;//模型映射,json格式
    base_url: string;//上游API地址
    platform_access_token: string;//平台访问令牌（仅type=7007时有效）
    billing_type: number;//计费类型（1-按量计费，2-按次计费）
    balance: number;//余额
    balance_updated_time: number;//余额更新时间
    channel_group_id: number;//渠道组ID
    config: string;//？？？
    created_time: number;//创建时间
    custom_balance_limit: number;//自定义余额限制
    custom_full_url_enabled: boolean;//自定义完整URL
    negative_optimization_enabled: boolean;//渠道负优化开关
    negative_optimization_time: number;//负优化时间
    negative_random_offset: number;//偏移量
    original_model_fake_resp_enabled: boolean;//原始模型假响应开关
    fake_completion_id_enabled: boolean;//伪造completion_id开关
    keyword_error_enabled: boolean;//关键词错误开关
    keyword_error: string[];//关键词错误
    exclude_custom_prompt_cost_enabled: boolean;//排除自定义提示成本
    force_chat_url_enabled: boolean;//强制ChatURL开关
    custom_system_prompt: string;//自定义系统提示
    disableReason: string;//最后禁用原因
    excluded_fields: string;//排除字段
    excluded_response_fields: string;//排除字段
    extra_fields: string;//额外字段
    extra_headers: string;//额外头部
    filter_non_stream_ad: boolean;//过滤非流广告
    filter_non_stream_ad_regex: string;//过滤非流广告正则
    filter_stream_ad: boolean;//过滤流广告
    filter_stream_ad_min_size: number;//过滤流广告最小尺寸
    filter_system_prompt: boolean;//过滤系统提示词
    function_call_enabled: boolean;//功能调用启用
    image_supported: boolean; // 是否支持图片输入
    image_in_markdown: boolean;//使用Markdown包裹图片链接（仅文生图模型有效）
    nonStrictTestMode: boolean;//非严格测试模式
    openai_organization: string;//OpenAI组织ID
    original_model_pricing: boolean;//使用原始模型定价（模型映射时有效）
    usage_recalculation_enabled: boolean;//usage重新计算,不信任上游返回的usage,自己重新计算,会消耗更多系统资源
    base64_image_prefix_mapping: string;//base64图片前缀映射 json格式
    request_token_limit_enabled: boolean;//请求令牌限制
    min_request_token_count: number;//最小请求令牌数量
    max_request_token_count: number;//最大请求令牌数量
    claude_stream_enabled: boolean;//Claude流式开关
    other: string;//其他
    overFrequencyAutoDisable: boolean;//超频自动禁用
    parse_url_to_content: boolean;//解析URL到内容
    parse_url_prefix_enabled: boolean;//是否启用自定义url前缀解析器,开启后,默认content开头以parse_url则解析
    parse_url_prefix: string;
    priority: number;//优先级，已经废弃，使用sort
    response_time: number;//响应时间
    retryInterval: number;//重试间隔
    sort: number;//优先级
    status: number;//状态
    testRequestBody: string;//测试请求体
    test_time: number;//测试时间
    type: number;//渠道类型
    used_quota: number;//已用配额
    weight: number;//权重
    transparent_proxy_enabled: boolean;//透明代理开关
    mj_base64_to_local_enabled: boolean;
    force_o1_stream_enabled: boolean;
    upstream_user_id: string;
    think_tag_processing_enabled: boolean;//是否启用<think>标签处理
    image_chat_conversion_enabled: boolean;//是否支持chat格式到image格式的转换
    image_chat_conversion_models: string;//支持图片转换的模型列表，逗号分隔
    convert_base64_to_url_enabled: boolean;//是否启用base64图片转换为URL
    image_server_url: string;//图片服务器地址
}

interface EditChannelFormModalProps {
    isEditChannelModalVisible: boolean;
    setIsEditChannelModalVisible: (isVisible: boolean) => void;
    editingChannelId: number;
    setEditingChannelId: (channelId: number) => void;
    isCloneMode: boolean;
    setIsCloneMode: (isCloneMode: boolean) => void;
    reload: () => void;
}

interface ChannelGroupType {
    id: number;
    type: number;
    status: number;
    name: string;
    weight: number;
    created_time: number;
    base_url: string;
    models: string;
    group: string;
    sort: number;
    overFrequencyAutoDisable: boolean;
    retryInterval: number;
}

interface UserGroupOption {
    key: string;
    text: string;
    value: string;
    description?: string;
    convert_ratio?: number;
    color_mapping?: string;
}

interface GroupOption {
    id: number;
    name: string;
    display_name: string;
    description?: string;
    convert_ratio?: number;
    color_mapping?: string;
    sort_order?: number;
}

const initialInputs = {
    name: '',
    type: 1,
    billing_type: 1,
    key: '',
    openai_organization: '',
    custom_balance_limit: undefined,//自定义余额限制,默认不填写
    base_url: '',
    order: 0,
    sort: 0,
    weight: 0,
    status: 1,//渠道状态，默认启用
    retryInterval: 300,
    testRequestBody: '',
    testRequestPreBody: '',
    overFrequencyAutoDisable: true,
    image_in_markdown: false,
    other: '',
    model_mapping: '',
    base64_image_prefix_mapping: '',
    extra_fields: '',
    excluded_fields: '',
    excluded_response_fields: '',
    channel_group_id: null,//渠道组 id
    models: [],
    group: ['default'],
    filter_stream_ad: false,//bool
    filter_non_stream_ad: false,//bool
    filter_stream_ad_min_size: 10,//int
    filter_non_stream_ad_regex: '',//正则表达式(非流式时)
    filter_system_prompt: false,//屏蔽系统预设提示词,bool
    custom_system_prompt: '',//自定义系统提示，varchar(255)
    extra_headers: '',//额外请求头，json
    platform_access_token: '',//平台访问令牌,仅可用于ShellApi
    parse_url_to_content: false,//解析URL为内容
    parse_url_prefix_enabled: false,//是否启用自定义url前缀解析器,开启后,默认content开头以parse_url则解析
    parse_url_prefix: '',//自定义解析url前缀
    custom_full_url_enabled: false,//自定义完整URL
    negative_optimization_enabled: false,//负优化开关
    negative_optimization_time: 0,//负优化时间,int
    negative_random_offset: 0,//偏移量,int
    original_model_fake_resp_enabled: false,//原始模型假响应开关
    fake_completion_id_enabled: false,//伪造completion_id开关
    exclude_custom_prompt_cost_enabled: false,//排除自定义提示成本
    force_chat_url_enabled: false,//强制ChatURL开关
    ignore_fc_tc_enabled: false,//忽略FC/TC相关请求内容
    arrange_messages: false,//整理消息
    function_call_enabled: true,//启用函数调用
    image_supported: true, // 默认支持图片输入
    original_model_pricing: false,//是否使用映射之前的原始模型计价
    channel_timeout_breaker_time: 0,//渠道请求超时熔断时间，默认0，或者-1表示不启用
    usage_recalculation_enabled:false,// usage重新计算,不信任上游返回的usage,自己重新计算,会消耗更多系统资源
    empty_response_error_enabled:false,// 空响应错误开关
    request_token_limit_enabled:false,// 请求令牌限制
    min_request_token_count:0,// 最小请求令牌数量
    max_request_token_count:0,// 最大请求令牌数量
    claude_stream_enabled:false,// Claude流式开关
    transparent_proxy_enabled:false,// 透明代理开关
    force_o1_stream_enabled: false,
    upstream_user_id: '',
    sync_both_db: false,//双数据库同步，默认关闭
    config: {
        convert_base64_to_url_enabled: false,//是否启用base64图片转换为URL，默认关闭
        image_server_url: '',//图片服务器地址，默认为空
    },
}

async function getUserGroupOptions(): Promise<UserGroupOption[]> {
    try {
        const res = await API.get(`/api/group/`)
        const { success, data } = res.data as { 
            success: boolean; 
            data: Array<{
                name: string;
                display_name: string;
                description?: string;
                convert_ratio?: number;
                color_mapping?: string;
            }>;
        };
        if (success) {
            return data.map((group) => ({
                key: group.name,
                text: group.display_name || group.name,
                value: group.name,
                description: group.description,
                convert_ratio: group.convert_ratio,
                color_mapping: group.color_mapping
            }));
        } else {
            return [];
        }
    } catch (error) {
        console.error('获取用户分组列表出错', error);
        showError(error);
        return [];
    }
}

async function getChannelGroups() {
    try {
        //确保获取了尽可能多的渠道组
        const res = await API.get("/api/channelGroup?p=0&pageSize=60")
        const {success, data} = res.data as { success: boolean; data: ChannelGroupType[] };
        if (success) {
            return data
        } else {
            return []
        }
    } catch (error) {
        console.error('获取渠道组列表出错', error)
        showError(error)
        return []
    }
}

interface creatChannelGroupRequest {
    name: string;
    models: string;
    group: string;
    base_url: string;
    sort: number;
    weight: number;
    overFrequencyAutoDisable: boolean;
    retryInterval: number;
}

// creatChannelGroup 创建渠道组
async function creatChannelGroup(info: creatChannelGroupRequest): Promise<boolean> {
    if (info.name === '' || info.models === '' || info.group === '') {
        return false
    }

    let channelGroupInputs = {
        name: info.name + '快速创建',
        models: info.models.split(',').filter((item) => item !== '').join(','),
        group: info.group.split(',').filter((item) => item !== '').join(','),
        base_url: info.base_url ?? '',
        sort: info.sort ?? 0,
        weight: info.weight ?? 0,
        overFrequencyAutoDisable: info.overFrequencyAutoDisable ?? true,
        retryInterval: info.retryInterval ?? 300,
    }

    try {
        const res = await API.post("/api/channelGroup/", channelGroupInputs)
        const {success} = res.data as { success: boolean; data: ChannelGroupType };
        return success
    } catch (error) {
        console.error('创建渠道组出错', error)
        showError(error)
        return false
    }
}

const IdentificationUpstreamChannelModal = lazy(() => import("./IdentificationUpstreamChannelModal"));

const GenerateRequestBodyModal = lazy(() => import("./GenerateRequestBodyModal"));

const model_mapping_example = {
    "userRequestModel": "actualUpstreamModel",
}
const base64_image_prefix_mapping_example = {
    "data:image/webp": "data:image/png",
}
const extra_fields_example = {
    "extra_field1": "extra_value1",
    "extra_field2": "extra_value2",
}

const EditChannelFormModal = ({
                                  isEditChannelModalVisible,
                                  setIsEditChannelModalVisible,
                                  editingChannelId,
                                  setEditingChannelId,
                                  isCloneMode,
                                  setIsCloneMode,
                                  reload
                              }: EditChannelFormModalProps) => {
    const { t } = useTranslation();  // 添加这行
    const {message: AntdMessage} = App.useApp();
    const [userState] = useContext(UserContext);
    const {groups, loading: groupsLoading, fetchGroups} = useGroup();
    const [editChannelForm] = Form.useForm();
    const [statusState, statusDispatch] = useContext(StatusContext);

    // 是否编辑模式，编辑模式下需要加载渠道信息，新增模式下不需要
    const isEditMode = editingChannelId > 0;
    const baseUrlRef = useRef<InputRef>(null);
    const keyRef = useRef<InputRef>(null);
// 在组件内部
    const [isEditable, setIsEditable] = useState(false);
    useEffect(() => {
        // 在组件挂载后的下一个事件循环中设置为可编辑
        const timer = setTimeout(() => setIsEditable(true), 0);
        return () => clearTimeout(timer);
    }, []);
    // 加载状态
    const [loading, setLoading] = useState(false);

    // 下拉框选项
    const [userGroupOptions, setUserGroupOptions] = useState<UserGroupOption[]>([]);
    const [channelGroups, setChannelGroups] = useState<ChannelGroupType[]>([]);

    // 是否批量创建
    const [batchCreate, setBatchCreate] = useState(false);
    const [prefixToRemove, setPrefixToRemove] = useState('');
    const [prefixToKeep, setPrefixToKeep] = useState('');


    // 输入的自定义模型名称
    const [inputCustomModel, setInputCustomModel] = useState<string>('');
    const [showPassword, setShowPassword] = useState(false);
    const [showAccessToken, setShowAccessToken] = useState(false);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const noAutocompleteStyle = {
        WebkitBoxShadow: '0 0 0px 1000px white inset'
    };
    // 监听表单字段变化，根据渠道类型显示不同的字段
    const selectedChannelType = Form.useWatch((values) => values.type, editChannelForm);
    const filterStreamAdEnabled = Form.useWatch((values) => values.filter_stream_ad, editChannelForm);
    const parseUrlPrefixEnabled = Form.useWatch((values) => values.parse_url_prefix_enabled, editChannelForm);
    const filterNonStreamAdEnabled = Form.useWatch((values) => values.filter_non_stream_ad, editChannelForm);
    const negativeOptimizationEnabled = Form.useWatch((values) => values.negative_optimization_enabled, editChannelForm);
    const requestTokenLimitEnabled = Form.useWatch((values) => values.request_token_limit_enabled, editChannelForm);
    const keywordErrorEnabled = Form.useWatch((values) => values.keyword_error_enabled, editChannelForm);
    const isMj = Form.useWatch((values) => {
        if (values.type === 7011 || values.type === 7006){
            return true
        }
        if (values.models && values.models.length) {
            for (let i = 0; i < values.models.length; i++) {
                if (values.models[i].startsWith("mj") || values.models[i].includes("midjourney")) {
                    return true
                }
            }
        }
        return false
    }, editChannelForm);


    // 识别上游渠道信息
    const [isIdentificationUpstreamChannelModalOpen, setIdentificationUpstreamChannelModalOpen] = useState(false);

    // 生成测试请求体
    const [isGenerateRequestBodyModalVisible, setIsGenerateRequestBodyModalVisible] = useState(false);

    const [modelTreeSelectData, setModelTreeSelectData] = useState(CHANNEL_MODEL_LIST)

    const channelTypeOptions = useMemo(() => {
        return CHANNEL_TYPE_OPTIONS.map((option) => ({
            key: option.value,
            label: option.text,
            value: option.value,
        }));
    }, []);
    const [config, setConfig] = useState({
        region: '',
        sk: '',
        ak: '',
        user_id: '',
        vertex_ai_project_id: '',
        vertex_ai_adc: ''
    });


    interface CurrentChannelConfig {
        config?: string;
        [key: string]: any;
    }

    const [currentChannelConfig, setCurrentChannelConfig] = useState<CurrentChannelConfig>({});

    const copyCurrentChannelConfig = () => {
        const channelConfig: CurrentChannelConfig = {
            ...currentChannelConfig,
            config: currentChannelConfig.config ? JSON.parse(currentChannelConfig.config) : {},
        };
        // 使用封装后的 copy 方法代替直接使用 navigator.clipboard
        copy(JSON.stringify(channelConfig))
            .then((success) => {
                if (success) {
                    AntdMessage.success('渠道配置已复制到剪贴板');
                } else {
                    AntdMessage.error('复制渠道配置失败');
                }
            });
    };
    const importChannelConfigFromClipboard = () => {
        // 尝试使用 Clipboard API 读取剪贴板内容
        let handleClipboardText = (text: string) => {
            try {
                let channelConfig = JSON.parse(text);

                // 保存当前的 ID
                const currentId = editChannelForm.getFieldValue('id');

                // 如果 base64_image_prefix_mapping 是 null，将其转换为空字符串
                if (channelConfig.base64_image_prefix_mapping === null) {
                    channelConfig.base64_image_prefix_mapping = '';
                }

                // 将逗号分隔的字符串重新转换为数组
                if (typeof channelConfig.models === 'string') {
                    channelConfig.models = channelConfig.models.split(',').filter(model => model.trim() !== '');
                }
                if (typeof channelConfig.group === 'string') {
                    channelConfig.group = channelConfig.group.split(',').filter(group => group.trim() !== '');
                }

                // 处理 config 字段
                if (typeof channelConfig.config === 'string') {
                    try {
                        channelConfig.config = JSON.parse(channelConfig.config);
                    } catch (e) {
                        console.error('Failed to parse config', e);
                        channelConfig.config = {};
                    }
                }

                // 处理 model_mapping 字段
                if (typeof channelConfig.model_mapping === 'string') {
                    try {
                        channelConfig.model_mapping = JSON.parse(channelConfig.model_mapping);
                    } catch (e) {
                        console.error('Failed to parse model_mapping', e);
                    }
                }
                // 确保 model_mapping 是格式化的 JSON 字符串
                if (typeof channelConfig.model_mapping === 'object') {
                    channelConfig.model_mapping = JSON.stringify(channelConfig.model_mapping, null, 2);
                }

                // 删除导入配置中的 ID
                delete channelConfig.id;

                // 如果是类型42,将 config 中的值设置到表单字段中
                if (channelConfig.type === 42 && channelConfig.config) {
                    const configObj = typeof channelConfig.config === 'string'
                        ? JSON.parse(channelConfig.config)
                        : channelConfig.config;

                    editChannelForm.setFieldsValue({
                        ...channelConfig,
                        id: currentId, // 保留当前的 ID
                        config: {
                            region: configObj.region,
                            vertex_ai_project_id: configObj.vertex_ai_project_id,
                            vertex_ai_adc: configObj.vertex_ai_adc,
                        }
                    });
                } else {
                    editChannelForm.setFieldsValue({
                        ...channelConfig,
                        id: currentId // 保留当前的 ID
                    });
                }

                AntdMessage.success('渠道配置已从剪贴板导入');
            } catch (error) {
                console.error('Error parsing clipboard content:', error);
                // @ts-ignore
                AntdMessage.error('解析剪贴板内容失败: ' + error.message);
            }
        };

        // 尝试使用标准的 Clipboard API
        if (navigator.clipboard && navigator.clipboard.readText) {
            navigator.clipboard.readText()
                .then(handleClipboardText)
                .catch((_) => {
                    // 如果 Clipboard API 失败，提示用户手动粘贴
                    AntdMessage.info('请手动粘贴渠道配置到下面的输入框中');
                    // 创建一个模态对话框让用户可以粘贴内容
                    Modal.confirm({
                        title: '请粘贴渠道配置',
                        content: (
                            <Input.TextArea 
                                rows={8} 
                                placeholder="请在此粘贴渠道配置信息"
                                onChange={(e) => {
                                    // 保存用户粘贴的内容
                                    (Modal.confirm as any).textAreaValue = e.target.value;
                                }}
                            />
                        ),
                        onOk: () => {
                            // 使用保存的值
                            if ((Modal.confirm as any).textAreaValue) {
                                handleClipboardText((Modal.confirm as any).textAreaValue);
                            }
                        },
                    });
                });
        } else {
            // 如果 Clipboard API 不可用，直接显示输入对话框
            Modal.confirm({
                title: '请粘贴渠道配置',
                content: (
                    <Input.TextArea 
                        rows={8} 
                        placeholder="请在此粘贴渠道配置信息"
                        onChange={(e) => {
                            // 保存用户粘贴的内容
                            (Modal.confirm as any).textAreaValue = e.target.value;
                        }}
                    />
                ),
                onOk: () => {
                    // 使用保存的值
                    if ((Modal.confirm as any).textAreaValue) {
                        handleClipboardText((Modal.confirm as any).textAreaValue);
                    }
                },
            });
        }
    };

    const handleConfigChange = (e) => {
        const { name, value } = e.target;
        setConfig(prevConfig => ({ ...prevConfig, [name]: value }));
    };

    async function getLatestModelList() {
        try {
            const res = await API.get("https://api.shell-api.com/models/tree-option", {skipDefaultErrorHandling: true})
            if (res.status === 200 && res.data) {
                setModelTreeSelectData(res.data)
                console.log("成功获取最新模型列表")
            }
        } catch (e) {
            showError(e, false)
            console.error("获取最新模型列表失败", e)
        }
    }

    const fetchUpstreamModelList = async () => {
        setLoading(true);
        try {
            let models = [];
            if (isEditMode) {
                const res = await API.get(`/api/channel/upstream_models/${editingChannelId}`);
                if (res.data && res.data.success) {
                    models = res.data.data;
                } else {
                    throw new Error(res.data.message || "获取模型列表失败");
                }
            } else {
                const key = editChannelForm.getFieldValue('key');
                if (!key) {
                    throw new Error("请填写密钥");
                }
                const baseUrl = editChannelForm.getFieldValue('base_url') || "https://api.openai.com";
                const host = new URL(baseUrl);
                const url = `https://${host.hostname}/v1/models`;
                const res = await API.get(url, {
                    headers: {
                        'Authorization': `Bearer ${key}`
                    }
                });
                if (res.data && res.data.data) {
                    models = res.data.data.map((model: any) => model.id);
                } else {
                    throw new Error("获取模型列表失败");
                }
            }
            const currentModels = editChannelForm.getFieldValue('models') || [];
            editChannelForm.setFieldsValue({
                models: Array.from(new Set([...currentModels, ...models]))
            });
            AntdMessage.success("获取模型列表成功");
        } catch (error) {
            showError(error instanceof Error ? error.message : '获取模型列表失败');
        } finally {
            setLoading(false);
        }
    };

    async function fillFormByChannelById(channelId: number) {
        setLoading(true);
        try {
            const res = await API.get(`/api/channel/${channelId}/`);
            const {success, data} = res.data as { success: boolean; data: ChannelType };
            if (success) {
                editChannelForm.setFieldsValue({
                    ...data,
                    models: data.models?.split(',').filter((item) => item !== '') ?? [],
                    group: data.group?.split(',').filter((item) => item !== '') ?? [],
                    model_mapping: safeStringifyJSON(safeParseJSON(data.model_mapping, ""), ""),
                    base64_image_prefix_mapping: safeStringifyJSON(safeParseJSON(data.base64_image_prefix_mapping, ""), ""),
                    extra_fields: safeStringifyJSON(safeParseJSON(data.extra_fields, ""), ""),
                    extra_headers: safeStringifyJSON(safeParseJSON(data.extra_headers, ""), ""),
                    channel_group_id: data.channel_group_id === -1 || data.channel_group_id === 0 ? null : data.channel_group_id,
                    config: safeParseJSON(data.config, ""),
                    keyword_error: safeParseJSON(data.keyword_error, ""),
                });
                setCurrentChannelConfig(data); // 保存当前渠道的配置
            } else {
                AntdMessage.error({content: '获取渠道信息失败：' + data, duration: 3});
            }
        } catch (e) {
            showError(e);
        } finally {
            setLoading(false);
        }
    }

    async function onFinish(values: any) {
        try {
            // 批量创建时，密钥为空或存在空行时，提示用户
            if (batchCreate && (!values.key || values.key.trim() === '' || values.key.split('\n').includes(''))) {
                if (!window.confirm('密钥为空或存在空行，是否继续提交？')) return
            }

            if (values.base_url && (values.base_url.includes(" ") || values.base_url.endsWith('/'))) {
                if (!window.confirm('代理地址不应包含空格或以/结尾，是否继续提交？')) {
                    return;
                }
            }

            if (values.parse_url_to_content) {
                if (!window.confirm('您已勾选始终解析URL为内容，是否继续提交？')) {
                    return;
                }
            }

            if (values.parse_url_prefix_enabled && values.parse_url_prefix === '') {
                if (!window.confirm('您已启用自定义URL前缀解析器，但未填写前缀，是否继续提交？')) {
                    return;
                }
            }

            console.log('Received values:', values);

            values.models = values.models.join(',');
            values.group = values.group.join(',');
            values.model_mapping = parseValueToSubmitString(values.model_mapping);
            values.base64_image_prefix_mapping = parseValueToSubmitString(values.base64_image_prefix_mapping);
            values.extra_fields = parseValueToSubmitString(values.extra_fields);
            values.extra_headers = parseValueToSubmitString(values.extra_headers);
            values.excluded_fields = values.excluded_fields?.split(',').filter((item: string) => item !== '').join(',');
            values.excluded_response_fields = values.excluded_response_fields?.split(',').filter((item: string) => item !== '').join(',');
            if (values.config && values.config.ak && values.config.sk) {
                values.key = `${values.config.ak}|${values.config.sk}|${values.config.region}`;
            }
            if (values.type === 42) {
                const configObj = {
                    region: values.config?.region,
                    vertex_ai_project_id: values.config?.vertex_ai_project_id,
                    vertex_ai_adc: values.config?.vertex_ai_adc,
                };
                values.config = JSON.stringify(configObj);
            } else if (values.config) {
                values.config = safeStringifyJSON(values.config, "", undefined);
            }
            // keyword_error单独处理转json
            values.keyword_error = safeStringifyJSON(values.keyword_error, "", undefined)
            //未选择渠道组时，编辑模式下传-1，新增/克隆模式下传null
            values.channel_group_id = values.channel_group_id ? values.channel_group_id : isEditMode ? -1 : null;
            
            // 构建API URL，根据sync_both_db参数决定是否添加同步参数
            const syncParam = values.sync_both_db ? '?syncBothDB=true' : '';
            const URL: string = `/api/channel/${syncParam}`;
            
            let res: AxiosResponse<any, any>
            if (isEditMode && !isCloneMode) {
                res = await API.put(URL, values);
            } else {
                delete values.id;
                res = await API.post(URL, values);
            }

            const {success, message} = res.data as { success: boolean; message: string };

            if (success) {
                AntdMessage.success(`渠道${isEditMode ? isCloneMode ? '克隆' : '更新' : '创建'}成功`);
                reload();
                setIsEditChannelModalVisible(false);
            } else {
                AntdMessage.error(`渠道${isEditMode ? isCloneMode ? '克隆' : '更新' : '创建'}失败：${message}`);
            }

        } catch (error) {
            showError(error);
        }
    }

    useEffect(() => {
        if (isEditChannelModalVisible && isEditMode && editingChannelId > 0) {
            fillFormByChannelById(editingChannelId).then();
        }
    }, [isEditChannelModalVisible, editingChannelId]);

    useEffect(() => {
        // getLatestModelList().then();
        getUserGroupOptions().then((options) => setUserGroupOptions(options))
        getChannelGroups().then((data) => setChannelGroups(data))
    }, []);

    // 只添加分组相关的 state 和函数
    const [groupOptions, setGroupOptions] = useState<GroupOption[]>([]);

    const fetchGroups = async () => {
        try {
            const res = await API.get('/api/groupPro/selectable?p=0&pageSize=100');
            const { success, data } = res.data;
            if (success) {
                setGroupOptions(data);
            }
        } catch (error) {
            showError(error);
        }
    };

    useEffect(() => {
        fetchGroups();
    }, []);

    function type2secretPrompt(type) {
        switch (type) {
            case 15:
                return '按照如下格式输入：APIKey|SecretKey';
            case 18:
                return '按照如下格式输入：APPID|APISecret|APIKey';
            case 22:
                return '按照如下格式输入：APIKey-AppId，例如：fastgpt-0sp2gtvfdgyi4k30jwlgwf1i-64f335d84283f05518e9e041';
            case 23:
                return '按照如下格式输入：AppId|SecretId|SecretKey';
            default:
                return '请输入渠道对应的鉴权密钥';
        }
    }
    return (
        <>
            {/*在组件的最外层添加一个隐藏的用户名字段，这可以进一步混淆浏览器的自动填充逻辑*/}
            <input type="text" style={{display: 'none'}} autoComplete="username"/>
            <Modal
                zIndex={1000}
                {...modalPropsWithFooter}
                title={isEditMode ? isCloneMode ? `克隆渠道（Form:#${editingChannelId}）` : `编辑渠道（#${editingChannelId}）` : '创建渠道'}
                open={isEditChannelModalVisible}
                onCancel={() => setIsEditChannelModalVisible(false)}
                afterClose={() => {
                    setEditingChannelId(0);
                    setIsCloneMode(false);
                    editChannelForm.resetFields();
                }}
                centered
                destroyOnClose={true}
                width={650}
                styles={{body: {maxHeight: '75vh', overflow: 'auto', overflowX: 'hidden', padding: 5,},}}
                onOk={() => editChannelForm.submit()}
                okText={isEditMode ? isCloneMode ? '克隆' : '保存' : '创建'}
                footer={[
                    <Button key="cancel" onClick={() => setIsEditChannelModalVisible(false)}>
                        取消
                    </Button>,
                    <Button
                        key="import"
                        onClick={importChannelConfigFromClipboard}
                        style={{ backgroundColor: '#52c41a', color: 'white', borderColor: '#52c41a' }}
                    >
                        从剪贴板导入
                    </Button>,
                    <Button
                        key="copy"
                        onClick={copyCurrentChannelConfig}
                        style={{ marginLeft: 8 }}
                    >
                        复制渠道配置
                    </Button>,
                    <Button
                        key="submit"
                        type="primary"
                        onClick={() => editChannelForm.submit()}
                        style={{ marginLeft: 8 }}
                    >
                        {isEditMode ? isCloneMode ? '克隆' : '保存' : '创建'}
                    </Button>,
                ]}
                // 避免多个弹窗开启时异常关闭
                mask={!isIdentificationUpstreamChannelModalOpen && !isGenerateRequestBodyModalVisible}
                keyboard={!isIdentificationUpstreamChannelModalOpen && !isGenerateRequestBodyModalVisible}
            >
                <Spin spinning={loading}>

                    <Form
                        form={editChannelForm}
                        name="editChannelForm"
                        onFinish={onFinish}
                        requiredMark={false}
                        autoComplete="off"
                        initialValues={initialInputs}
                        style={{padding: 8}}
                        labelCol={{span: 6}}
                        wrapperCol={{span: 17}}
                        scrollToFirstError
                    >
                        <Form.Item label="渠道ID" name="id" hidden/>

                        <Form.Item label="渠道名称" name="name" rules={[{required: true, message: '请输入渠道名称'}]}>
                            <Input/>
                        </Form.Item>

                        <Form.Item label="备注" name="remark">
                            <Input placeholder="请输入渠道备注信息" />
                        </Form.Item>

                        <Form.Item label="平台类型" name="type" rules={[{required: true, message: '请选择渠道平台'}]}>
                            <Select
                                placeholder="请选择渠道平台"
                                options={channelTypeOptions}
                            />
                        </Form.Item>

                        <Form.Item
                            name="billing_type"
                            label="计费类型"
                            rules={[{required: true, message: '请选择计费类型'}]}
                        >
                            <Select
                                options={[
                                    {key: 1, label: '按量计费', value: 1},
                                    {key: 2, label: '按次计费', value: 2},
                                ]}
                            />
                        </Form.Item>

                        <Form.Item
                            label={t('channelsTable.columns.group')}
                            name="group"
                            rules={[{ required: true, message: t('common.selectPlaceholder', { name: t('channelsTable.columns.group') }) }]}
                        >
                            <Select
                                mode="multiple"
                                allowClear
                                showSearch
                                placeholder={t('channelsTable.placeholder.selectGroup')}
                                optionFilterProp="label"
                                options={groupOptions.map(group => ({
                                    label: (
                                        <Tooltip title={
                                            <div>
                                                <div><strong>{t('common.name')}:</strong> {group.name}</div>
                                                {group.display_name !== group.name && (
                                                    <div><strong>{t('common.displayName')}:</strong> {group.display_name}</div>
                                                )}
                                                {group.description && (
                                                    <div><strong>{t('common.description')}:</strong> {group.description}</div>
                                                )}
                                                {group.convert_ratio && (
                                                    <div><strong>{t('common.ratio')}:</strong> {group.convert_ratio}x</div>
                                                )}
                                            </div>
                                        }>
                                            <Tag 
                                                color={group.color_mapping || 'default'}
                                                style={{ margin: 0 }}
                                            >
                                                {group.display_name}
                                                {group.display_name !== group.name && (
                                                    <Typography.Text type="secondary" style={{ fontSize: '12px', marginLeft: 4 }}>
                                                        ({group.name})
                                                    </Typography.Text>
                                                )}
                                            </Tag>
                                        </Tooltip>
                                    ),
                                    value: group.name
                                }))}
                                optionLabelProp="label"
                                tagRender={(props) => {
                                    const group = groupOptions.find(g => g.name === props.value);
                                    return (
                                        <Tag
                                            color={group?.color_mapping || 'default'}
                                            closable={props.closable}
                                            onClose={props.onClose}
                                            style={{ marginRight: 3 }}
                                        >
                                            {group?.display_name || props.value}
                                        </Tag>
                                    );
                                }}
                                filterOption={(input, option) => {
                                    if (!option?.label) return false;
                                    const label = option.label.toString().toLowerCase();
                                    const value = option.value.toString().toLowerCase();
                                    const searchText = input.toLowerCase();
                                    return label.includes(searchText) || value.includes(searchText);
                                }}
                                style={{ width: '100%' }}
                            />
                        </Form.Item>

                        <Form.Item
                            label="可用模型"
                            name="models"
                            rules={[{required: true}]}
                            extra={
                                <Button
                                    size="middle"
                                    icon={<SyncOutlined/>}
                                    onClick={fetchUpstreamModelList}
                                    style={{marginTop: 8}}
                                >
                                    获取上游模型
                                </Button>
                            }
                        >
                            <TreeSelect
                                allowClear
                                treeCheckable
                                treeLine
                                // placement={"topLeft"}
                                treeData={modelTreeSelectData}
                                placeholder="该渠道支持的模型"
                            />
                        </Form.Item>
                        <Form.Item label="模型操作">
                            <Flex vertical gap={8}>
                                <Flex gap={8}>
                                    <Button
                                        onClick={() => {
                                            const azureModels = [
                                                'gpt-3.5-turbo',
                                                'gpt-3.5-turbo-0125',
                                                'gpt-3.5-turbo-0301',
                                                'gpt-3.5-turbo-0613',
                                                'gpt-3.5-turbo-1106',
                                                'gpt-3.5-turbo-16k',
                                                'gpt-3.5-turbo-16k-0613',
                                                'gpt-4',
                                                'gpt-4-0125-preview',
                                                'gpt-4-0613',
                                                'gpt-4-1106-preview',
                                                'gpt-4-32k',
                                                'gpt-4-32k-0613',
                                                'gpt-4-turbo',
                                                'gpt-4-turbo-2024-04-09',
                                                'gpt-4-turbo-preview',
                                                'gpt-4-vision-preview',
                                                'gpt-4o',
                                                'gpt-4o-2024-05-13',
                                                'gpt-4o-2024-08-06',
                                                'gpt-4o-mini',
                                                'gpt-4o-mini-2024-07-18',
                                                'text-embedding-3-large',
                                                'text-embedding-3-small',
                                                'text-embedding-ada-002',
                                                'whisper-1'
                                            ];
                                            const currentModels = editChannelForm.getFieldValue('models') || [];
                                            editChannelForm.setFieldsValue({
                                                models: [...new Set([...currentModels, ...azureModels])]
                                            });
                                            message.success('Azure 模型已添加');
                                        }}
                                    >
                                        添加OpenAI常用模型
                                    </Button>
                                </Flex>
                                <Flex gap={8} wrap>
                                    <Input
                                        placeholder="输入要移除的模型前缀"
                                        value={prefixToRemove}
                                        onChange={(e) => setPrefixToRemove(e.target.value)}
                                        style={{ minWidth: '180px', flex: '0 0 180px' }}
                                    />
                                    <Button
                                        onClick={() => {
                                            if (!prefixToRemove) {
                                                message.warning('请输入要移除的模型前缀');
                                                return;
                                            }
                                            const currentModels = editChannelForm.getFieldValue('models') || [];
                                            const filteredModels = currentModels.filter(model => !model.startsWith(prefixToRemove));
                                            if (currentModels.length === filteredModels.length) {
                                                message.info('没有找到匹配的模型');
                                            } else {
                                                editChannelForm.setFieldsValue({
                                                    models: filteredModels
                                                });
                                                message.success(`已移除前缀为 "${prefixToRemove}" 的模型`);
                                            }
                                            setPrefixToRemove('');
                                        }}
                                    >
                                        移除指定前缀模型
                                    </Button>
                                    <Input
                                        placeholder="输入要保留的模型前缀"
                                        value={prefixToKeep}
                                        onChange={(e) => setPrefixToKeep(e.target.value)}
                                        style={{ minWidth: '180px', flex: '0 0 180px' }}
                                    />
                                    <Button
                                        onClick={() => {
                                            if (!prefixToKeep) {
                                                message.warning('请输入要保留的模型前缀');
                                                return;
                                            }
                                            const currentModels = editChannelForm.getFieldValue('models') || [];
                                            const filteredModels = currentModels.filter(model => model.startsWith(prefixToKeep));
                                            if (filteredModels.length === 0) {
                                                message.info('没有找到匹配的模型');
                                            } else {
                                                editChannelForm.setFieldsValue({
                                                    models: filteredModels
                                                });
                                                message.success(`已保留前缀为 "${prefixToKeep}" 的模型，共 ${filteredModels.length} 个`);
                                            }
                                            setPrefixToKeep('');
                                        }}
                                    >
                                        只保留指定前缀模型
                                    </Button>
                                </Flex>
                            </Flex>
                        </Form.Item>
                        <Form.Item label="自定义模型">
                            <Flex gap={8}>
                                <Input
                                    value={inputCustomModel}
                                    onChange={(e) => setInputCustomModel(e.target.value)}
                                    placeholder="模型名称或逗号分隔的模型名称"
                                    style={{width: "60%"}}
                                />
                                <Button
                                    onClick={() => {
                                        const customModel = inputCustomModel.trim()
                                        if (customModel === '') {
                                            return
                                        } else if (typeof editChannelForm.getFieldValue('models') !== 'object') {
                                            editChannelForm.setFieldsValue({models: [customModel]})
                                        } else {
                                            editChannelForm.setFieldsValue({
                                                models: [...new Set([...editChannelForm.getFieldValue('models'), customModel])]
                                            })
                                            setInputCustomModel('')
                                        }
                                    }}
                                >
                                    添加
                                </Button>
                                <Button
                                    onClick={() => {
                                        //模型名称可能是a,b,c 或 "a","b","c"
                                        const customModel = inputCustomModel.trim();
                                        if (customModel === '') {
                                            return;
                                        } else if (typeof editChannelForm.getFieldValue('models') !== 'object') {
                                            editChannelForm.setFieldsValue({models: [customModel]});
                                        } else {
                                            const models = customModel.split(',').map((item) => item.replace(/"/g, ''));
                                            const uniqueModels = [...new Set([...editChannelForm.getFieldValue('models'), ...models])];
                                            editChannelForm.setFieldsValue({
                                                models: uniqueModels,
                                            });
                                            setInputCustomModel('');
                                        }
                                    }}
                                >
                                    批量添加
                                </Button>
                            </Flex>
                        </Form.Item>
                        <Form.Item 
                            label="代理地址" 
                            name="base_url"
                            rules={[{type: 'url', message: '请输入正确的URL', warningOnly: true}]}
                            tooltip={
                                Form.useWatch('models', editChannelForm)?.includes('search-serper') ? 
                                '注意: 使用 search-serper 模型时，请确认上游URL是否需要 /search/serper 后缀。有的上游直接使用域名，有的需要加 /search/serper 后缀，具体请咨询上游服务商。' : 
                                undefined
                            }
                        >
                            <Input
                                disabled={!isEditable}
                                autoComplete="new-password"
                                data-lpignore="true"
                                name={`base_url_${Math.random().toString(36).substr(2, 9)}`}
                                placeholder="上游API请求地址，使用平台默认地址时无需填写"
                                suffix={
                                    <SettingFilled onClick={() => setIdentificationUpstreamChannelModalOpen(true)}/>
                                }
                            />
                        </Form.Item>

                        {selectedChannelType !== 33 && selectedChannelType !== 42 && (
                            <Form.Item 
                                label="密钥" 
                                name="key" 
                                rules={[{required: true, message: '请输入密钥'}]}
                                extra={
                                    <>
                                        <Switch
                                            checkedChildren="多行"
                                            unCheckedChildren="单行"
                                            checked={batchCreate}
                                            onChange={(checked) => setBatchCreate(checked)}
                                        />
                                        {batchCreate && (
                                            <div style={{ marginTop: '8px', fontSize: '12px', color: '#888' }}>
                                                支持同时导入代理配置，格式：密钥----代理地址，例如：
                                                <br />
                                                sk-xxxx----socks5://user:pass@host:port
                                            </div>
                                        )}
                                    </>
                                }
                            >
                                {batchCreate || [15, 18, 22, 23].includes(selectedChannelType) ? (
                                    <Input.TextArea
                                        disabled={!isEditable}
                                        autoComplete="new-password"
                                        data-lpignore="true"
                                        name={`key_${Math.random().toString(36).substr(2, 9)}`}
                                        placeholder={type2secretPrompt(selectedChannelType)}
                                        autoSize={{minRows: 2, maxRows: 6}}
                                    />
                                ) : (
                                    <Input
                                        disabled={!isEditable}
                                        placeholder={type2secretPrompt(selectedChannelType)}
                                        type={showPassword ? "text" : "password"}
                                        autoComplete="new-password"
                                        data-lpignore="true"
                                        name={`key_${Math.random().toString(36).substr(2, 9)}`}
                                        suffix={
                                            <Button
                                                type="text"
                                                size="small"
                                                icon={showPassword ? <EyeOutlined/> : <EyeInvisibleOutlined/>}
                                                onClick={() => setShowPassword(!showPassword)}
                                            />
                                        }
                                    />
                                )}
                            </Form.Item>
                        )}

                        <Form.Item label="批量创建" hidden={isEditMode || isCloneMode || selectedChannelType === 33}>
                            <Switch checked={batchCreate} onChange={setBatchCreate}/>
                        </Form.Item>

                        <Divider orientation="right">附加信息</Divider>

                        {selectedChannelType === 3 && //Azure
                            <Form.Item
                                name="other"
                                label="默认 API 版本"
                                tooltip="模型部署名称必须和模型名称保持一致"
                            >
                                <Input
                                    placeholder="请输入默认 API 版本，例如：2023-06-01-preview，该配置可以被实际的请求查询参数所覆盖"/>
                            </Form.Item>
                        }

                        {selectedChannelType === 18 && //讯飞星火
                            <Form.Item
                                name="other"
                                label="模型版本"
                            >
                                <Input placeholder="请输入星火大模型版本，注意是接口地址中的版本号，例如：v3.1"/>
                            </Form.Item>
                        }

                        {selectedChannelType === 21 &&
                            <Form.Item name="other" label="知识库 ID">
                                <Input placeholder="请输入知识库 ID，例如：123456"/>
                            </Form.Item>
                        }

                        {selectedChannelType === 33 &&   //AWS Claude
                            <>
                                <Form.Item name={["config", "region"]} label="Region" rules={[{required: true}]}>
                                    <Input placeholder="请输入Region，例如：us-east-1"/>
                                </Form.Item>
                                <Form.Item name={["config", "ak"]} label="IAM Access Key" rules={[{required: true}]}>
                                    <Input placeholder="请输入Access Key"/>
                                </Form.Item>
                                <Form.Item name={["config", "sk"]} label="IAM Secret Key" rules={[{required: true}]}>
                                    <Input placeholder="请输入Secret Key"/>
                                </Form.Item>
                            </>
                        }

                        {selectedChannelType === 42 && (
                            <>
                                <Form.Item
                                    label="Region"
                                    name={["config", "region"]}
                                    rules={[{required: true, message: '请输入 Vertex AI Region'}]}
                                >
                                    <Input placeholder="Vertex AI Region, e.g. us-east5"/>
                                </Form.Item>
                                <Form.Item
                                    label="Vertex AI Project ID"
                                    name={["config", "vertex_ai_project_id"]}
                                    rules={[{required: true, message: '请输入 Vertex AI Project ID'}]}
                                >
                                    <Input placeholder="Vertex AI Project ID"/>
                                </Form.Item>
                                <Form.Item
                                    label="Google Cloud ADC"
                                    name={["config", "vertex_ai_adc"]}
                                    rules={[{
                                        required: true,
                                        message: '请输入 Google Cloud Application Default Credentials JSON'
                                    }]}
                                >
                                    <Input.TextArea
                                        placeholder="Google Cloud Application Default Credentials JSON"
                                        autoSize={{minRows: 3, maxRows: 6}}
                                    />
                                </Form.Item>
                            </>
                        )}


                        {isMj && // MJ
                            <>
                                <Form.Item
                                    name={["config", "mj_translate_enabled"]}
                                    label="开启MJ预翻译"
                                    tooltip={"开启mj预先翻译,会先调用gpt或者其他模型进行翻译,可能会增加mj的响应时间"}
                                    valuePropName="checked"
                                >
                                    <Switch/>
                                </Form.Item>
                                <Form.Item name={["config", "mj_translate_model"]} label="MJ翻译模型">
                                    <Input placeholder="请输入gpt-3.5-turbo或其他模型"/>
                                </Form.Item>
                                {/*是否拼接前缀*/}
                                <Form.Item
                                    name={["config", "mj_concat_mode_url_prefix_enabled"]}
                                    label="拼接URL前缀"
                                    tooltip={"自动在请求上游的URL前拼接/mj-relax,/mj-turbo,需要上游支持通过此路径识别模式"}
                                    valuePropName="checked"
                                >
                                    <Switch/>
                                </Form.Item>
                                {/*是否在prompt后拼接模式*/}
                                <Form.Item
                                    name={["config", "mj_concat_mode_to_content"]}
                                    label="拼接后缀命令"
                                    tooltip={"自动在请求上游的prompt后拼接--relax,--turbo模式,需要上游支持通过此路径识别模式"}
                                    valuePropName="checked"
                                >
                                    <Switch/>
                                </Form.Item>
                                {/*是否使用accountFilter请求上游模式*/}
                                <Form.Item
                                    name={["config", "mj_account_filter_mode_enabled"]}
                                    label="参数传递模式"
                                    tooltip={"自动在请求上游的参数中添加accountFilter传递模式,需要上游支持识别此方式,如果自建MJP可以考虑开启此项"}
                                    valuePropName="checked"
                                >
                                    <Switch/>
                                </Form.Item>
                                <Form.Item
                                    name={["config", "mj_base64_to_local_enabled"]}
                                    label="Base64本地存储"
                                    tooltip={"开启后,将base64内容存储到本地并插入到prompt前面"}
                                    valuePropName="checked"
                                >
                                    <Switch/>
                                </Form.Item>

                            </>
                        }
                        {statusState.status.eise &&
                            <>
                                <Form.Item
                                    name={["config", "eise"]}
                                    label="eise开关"
                                    tooltip="是否开启,默认0,为0则不替换系统默认配置,开启为1,关闭为2"
                                >
                                    <Select
                                        style={{ width: '100%' }}
                                        placeholder="请选择eise开关设置"
                                    >
                                        <Option value={0}>
                                            <SettingTwoTone twoToneColor="#1890ff" /> 使用系统默认配置
                                        </Option>
                                        <Option value={1}>
                                            <CheckCircleTwoTone twoToneColor="#52c41a" /> 开启
                                        </Option>
                                        <Option value={2}>
                                            <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 关闭
                                        </Option>
                                    </Select>
                                </Form.Item>
                                <Form.Item name={["config", "eise_url"]} label="eise url"
                                           tooltip="eise url 多个可以逗号拼接"
                                >
                                    <Input placeholder="eise url 多个可以逗号拼接"/>
                                </Form.Item>
                                <Form.Item name={["config", "eise_key"]} label="eise key 32位"
                                           tooltip="必须32位不能多也不能少"
                                >
                                    <Input placeholder="eise key 32位"/>
                                </Form.Item>
                            </>
                        }
                        <Form.Item
                            name={["config", "trust_upstream_stream_usage"]}
                            label="信任上游流式用量"
                            tooltip="是否完全信任上游流式返回的用量统计，关闭则重新计算"
                        >
                            <Select
                                style={{ width: '100%' }}
                                placeholder="请选择是否信任上游流式用量统计"
                            >
                                <Option value={0}>
                                    <SettingTwoTone twoToneColor="#1890ff" /> 使用系统默认配置
                                </Option>
                                <Option value={1}>
                                    <CheckCircleTwoTone twoToneColor="#52c41a" /> 开启
                                </Option>
                                <Option value={2}>
                                    <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 关闭
                                </Option>
                            </Select>
                        </Form.Item>

                        <Form.Item
                            name={["config", "force_stream_option"]}
                            label="强制上游返回用量"
                            tooltip="是否强制要求上游在流式响应中返回用量统计信息，可能会增加响应延迟"
                        >
                            <Select
                                style={{ width: '100%' }}
                                placeholder="请选择是否强制上游返回用量"
                            >
                                <Option value={0}>
                                    <SettingTwoTone twoToneColor="#1890ff" /> 使用系统默认配置
                                </Option>
                                <Option value={1}>
                                    <CheckCircleTwoTone twoToneColor="#52c41a" /> 开启
                                </Option>
                                <Option value={2}>
                                    <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 关闭
                                </Option>
                            </Select>
                        </Form.Item>

                        <Form.Item
                            name={["config", "force_downstream_stream_usage"]}
                            label="强制下游流式用量"
                            tooltip="是否强制在流式响应中返回用量统计给下游，可能会增加响应延迟"
                        >
                            <Select
                                style={{ width: '100%' }}
                                placeholder="请选择是否强制返回下游流式用量"
                            >
                                <Option value={0}>
                                    <SettingTwoTone twoToneColor="#1890ff" /> 使用系统默认配置
                                </Option>
                                <Option value={1}>
                                    <CheckCircleTwoTone twoToneColor="#52c41a" /> 开启
                                </Option>
                                <Option value={2}>
                                    <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 关闭
                                </Option>
                            </Select>
                        </Form.Item>

                        <Form.Item
                            name="think_tag_processing_enabled"
                            label="<think>标签处理"
                            tooltip="是否启用<think>标签处理，针对DeepSeek-R1等支持思考过程的模型，将思考内容提取到reasoning_content字段"
                        >
                            <Select
                                style={{ width: '100%' }}
                                placeholder="请选择是否启用<think>标签处理"
                            >
                                <Option value={false}>
                                    <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 关闭
                                </Option>
                                <Option value={true}>
                                    <CheckCircleTwoTone twoToneColor="#52c41a" /> 开启
                                </Option>
                            </Select>
                        </Form.Item>

                        <Form.Item name="channel_group_id" label="渠道组">
                            <Select
                                allowClear
                                showSearch
                                id="channel-group-select"
                                open={isDropdownOpen}
                                onDropdownVisibleChange={(open) => setIsDropdownOpen(open)}
                                options={
                                    channelGroups.map((group) => ({
                                        key: group.id,
                                        label: group.name,
                                        value: group.id,
                                    }))
                                }
                                dropdownStyle={{...noAutocompleteStyle}}
                                dropdownClassName="no-autocomplete"
                                dropdownRender={menu => (
                                    <>
                                        {menu}
                                        <Divider style={{margin: '4px 0'}}/>
                                        <Button
                                            block
                                            type="text"
                                            icon={<PlusOutlined/>}
                                            onClick={() => {
                                                const getInfo = {
                                                    name: editChannelForm.getFieldValue('name'),
                                                    models: editChannelForm.getFieldValue('models').join(','),
                                                    group: editChannelForm.getFieldValue('group').join(','),
                                                    base_url: editChannelForm.getFieldValue('base_url'),
                                                    sort: editChannelForm.getFieldValue('sort'),
                                                    weight: editChannelForm.getFieldValue('weight'),
                                                    overFrequencyAutoDisable: editChannelForm.getFieldValue('overFrequencyAutoDisable'),
                                                    retryInterval: editChannelForm.getFieldValue('retryInterval'),
                                                    undeadModeEnabled: editChannelForm.getFieldValue('undeadModeEnabled'),
                                                }
                                                if (getInfo.name === '' || getInfo.models === '' || getInfo.group === '') {
                                                    AntdMessage.warning({
                                                        content: '请先填写渠道基础信息',
                                                        duration: 2
                                                    }).then()
                                                    return
                                                } else {
                                                    AntdMessage.loading({
                                                        content: '创建中...',
                                                        duration: 0,
                                                        key: 'creat-c-g'
                                                    })
                                                    creatChannelGroup(getInfo)
                                                        .then((success) => {
                                                            if (success) {
                                                                AntdMessage.success({
                                                                    content: '创建成功',
                                                                    duration: 0.5,
                                                                    key: 'creat-c-g'
                                                                }).then()
                                                                    .then(() => getChannelGroups()
                                                                        .then((data) => setChannelGroups(data)))
                                                            } else {
                                                                AntdMessage.error({
                                                                    content: '创建失败',
                                                                    duration: 2,
                                                                    key: 'creat-c-g'
                                                                }).then()
                                                            }
                                                        })
                                                }
                                            }}
                                        >
                                            快速创建
                                        </Button>
                                    </>
                                )}
                            />
                        </Form.Item>

                        {selectedChannelType === 7007 ? (
                            <>
                                <Form.Item
                                    label="访问令牌"
                                    name="platform_access_token"
                                >
                                    <Input
                                        placeholder="Shell API 访问令牌，填写后查询余额时返回账户余额"
                                        type={showAccessToken ? "text" : "password"}
                                        autoComplete="new-password"
                                        data-lpignore="true"
                                        name={`access_token_${Math.random().toString(36).substr(2, 9)}`}
                                        onChange={(e) => {
                                            const value = e.target.value;
                                            editChannelForm.setFieldsValue({platform_access_token: value});
                                        }}
                                        suffix={
                                            <Button
                                                type="text"
                                                size="small"
                                                icon={showAccessToken ? <EyeOutlined/> : <EyeInvisibleOutlined/>}
                                                onClick={() => setShowAccessToken(!showAccessToken)}
                                            />
                                        }
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="上游用户ID"
                                    name="upstream_user_id"
                                    tooltip={
                                        <div>
                                            <p>非必填项。用于与平台访问令牌配合使用的上游用户ID。</p>
                                            <p>如果更新渠道余额时遇到 401/403 错误，可尝试填写此字段。</p>
                                        </div>
                                    }
                                >
                                    <Input placeholder="非必填，遇到余额更新 401/403 错误时填写" />
                                </Form.Item>
                            </>
                        ) : (
                            <Form.Item label="自定义渠道总额" name="custom_balance_limit">
                                <InputNumber suffix="美金" precision={2} style={{width: 120}}/>
                            </Form.Item>
                        )}

                        <Divider orientation="right">调度配置</Divider>

                        <Form.Item name="sort" label="优先级">
                            <InputNumber min={0} max={9999999}/>
                        </Form.Item>

                        <Form.Item name="weight" label="渠道权重">
                            <InputNumber min={0} max={9999999}/>
                        </Form.Item>

                        <Form.Item 
                            name="cost_per_unit" 
                            label="渠道成本(Beta)"
                            tooltip="每1美元的成本,用于计算渠道利润"
                        >
                            <InputNumber
                                min={0}
                                max={999999}
                                precision={6}
                                step={0.000001}
                                placeholder="请输入每1美元的成本"
                                addonAfter="$/美元"
                            />
                        </Form.Item>

                        {selectedChannelType === 1 &&//OpenAI
                            <Form.Item
                                label="组织代码"
                                name="openai_organization"
                                tooltip="此项可选，用于控制调用时使用的组织"
                            >
                                <Input/>
                            </Form.Item>
                        }

                        <Form.Item
                            name="function_call_enabled"
                            label="支持TC/FC请求"
                            tooltip="Tool Choice/Function Call请求只会分发到开启此选项的渠道"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name="image_supported"
                            label="支持图片输入"
                            valuePropName="checked"
                            tooltip="是否支持图片输入，只有开启此选项的渠道才会接收包含图片的请求"
                        >
                            <Switch/>
                        </Form.Item>


                        <Divider orientation="right">测试与重试</Divider>
                        <Form.Item
                            name="retryInterval"
                            label="重试周期"
                            tooltip="自动禁用渠道后的重试周期，单位秒，-1为不死模式"
                        >
                            <InputNumber
                                min={-1}
                                step={60}
                                suffix={"秒"}
                                addonAfter={
                                    <SunFilled onClick={() => {
                                        editChannelForm.setFieldsValue({undeadModeEnabled: true})
                                        AntdMessage.success({content: '已设为不死模式', duration: 0.5}).then()
                                    }}/>
                                }
                            />
                        </Form.Item>
                        <Form.Item
                            name="undeadModeEnabled"
                            label="不死模式"
                            tooltip={"开启不死模式,和之前的-1并不冲突,主要用于在自定义熔断之后不占用复活时间这个配置项"}
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            name="testRequestBody"
                            label="测试请求体"
                            rules={[jsonValidationRule]}
                            tooltip="渠道测试和重启时使用的请求体，不填写默认使用 gpt-3.5-turbo 模型"
                        >
                            <Input.TextArea autoSize={{minRows: 2}}/>
                        </Form.Item>

                        <Form.Item label="快捷生成请求体">
                            <Button onClick={() => setIsGenerateRequestBodyModalVisible(true)}>生成</Button>
                        </Form.Item>

                        <Form.Item
                            name="overFrequencyAutoDisable"
                            label="超频自动熔断"
                            tooltip={"上游返回超过频率限制类错误时自动禁用渠道"}
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name="nonStrictTestMode"
                            label="宽松测试模式"
                            tooltip="默认只有消息内容返回1才算成功，宽松模式下状态200即视为成功"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>


                        <Divider orientation="right">请求前转换</Divider>
                        <Form.Item label="模型映射" name="model_mapping" rules={[jsonValidationRule]}>
                            <Input.TextArea
                                autoSize={{minRows: 3}}
                                placeholder={safeStringifyJSON(model_mapping_example, '')}
                            />
                        </Form.Item>

                        <Form.Item label="base64图片前缀映射" name="base64_image_prefix_mapping"
                                   rules={[jsonValidationRule]}>
                            <Input.TextArea
                                autoSize={{minRows: 3}}
                                placeholder={safeStringifyJSON(base64_image_prefix_mapping_example, '')}
                            />
                        </Form.Item>

                        <Form.Item
                            name="request_token_limit_enabled"
                            label="请求Token限制"
                            tooltip="开启后将限制渠道请求Token数,如果超过范围则会重试其他渠道,也就是说会浪费掉一次重试的次数,所以请合理安排渠道优先级"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        {requestTokenLimitEnabled && (
                            <>
                                <Form.Item
                                    label="请求Token数范围"
                                    required={false}
                                >
                                    <Space>
                                        <Form.Item
                                            name="min_request_token_count"
                                            noStyle
                                        >
                                            <InputNumber min={0} placeholder="最小值"/>
                                        </Form.Item>
                                        <span>-</span>
                                        <Form.Item
                                            name="max_request_token_count"
                                            noStyle
                                        >
                                            <InputNumber min={0} placeholder="最大值"/>
                                        </Form.Item>
                                    </Space>
                                </Form.Item>
                            </>
                        )}

                        <Form.Item
                            name="keyword_error_enabled"
                            label="关键词报错"
                            tooltip="开启后,最后一句聊天记录触发关键词将会报错,不发请求,如果开启了重试会重试其他渠道,用于内部重试其他渠道,也就是说会浪费掉一次重试的次数,所以请合理安排渠道优先级"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        {keywordErrorEnabled && (
                            <Form.Item
                                label='关键词'
                                name="keyword_error"
                                extra={'关键词,回车确认'}
                            >
                                <Select mode="tags" options={[
                                    {label: '鲁迅', value: '鲁迅'},
                                    {label: '周树人', value: '周树人'},
                                    {label: '西红柿', value: '西红柿'},
                                    {label: '钢丝球', value: '钢丝球'},
                                    {label: 'GPT', value: 'GPT'},
                                    {label: '你是谁', value: '你是谁'},
                                ]}/>
                            </Form.Item>
                        )}

                        <Form.Item
                            name="original_model_pricing"
                            label="原始模型计价"
                            tooltip="模型映射时使用原始模型记录日志和计费"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name="usage_recalculation_enabled"
                            label="usage重新计算"
                            tooltip="usage重新计算,不信任上游返回的usage,自己重新计算,会消耗更多系统资源"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name="empty_response_error_enabled"
                            label="空返报错"
                            tooltip="如果上游返回空响应则报错"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name="remove_image_download_error_enabled"
                            label="链接报错删除"
                            tooltip="如果链接报错则从请求中删除此链接"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name="extra_headers"
                            label="额外请求头"
                            rules={[jsonValidationRule]}
                        >
                            <Input.TextArea autoSize={{minRows: 2}}
                                            placeholder={'JSON格式，例如 {"aa": "bb"}'}
                            />
                        </Form.Item>

                        <Form.Item
                            name="excluded_fields"
                            label="排除请求字段"
                            rules={[{...commaSeparatedStringValidationRule, warningOnly: true}]}
                        >
                            <Input.TextArea autoSize={{minRows: 1}}
                                            placeholder={'输入字段名，多个用英文逗号分隔，例如 field1,field2'}
                            />
                        </Form.Item>
                        <Form.Item
                            name="excluded_response_fields"
                            label="排除响应字段"
                            rules={[{...commaSeparatedStringValidationRule, warningOnly: true}]}
                        >
                            <Input.TextArea autoSize={{minRows: 1}}
                                            placeholder={'输入字段名，多个用英文逗号分隔，例如 field1,field2'}
                            />
                        </Form.Item>

                        <Form.Item
                            name="extra_fields"
                            label="额外请求字段"
                            rules={[jsonValidationRule]}
                        >
                            <Input.TextArea autoSize={{minRows: 4}}
                                            placeholder={safeStringifyJSON(extra_fields_example, '')}
                            />
                        </Form.Item>

                        <Form.Item
                            name="filter_system_prompt"
                            label="屏蔽系统提示词"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name="custom_system_prompt"
                            label="预设系统提示词"
                        >
                            <Input.TextArea autoSize={{minRows: 2}}/>
                        </Form.Item>


                        <Form.Item
                            name="arrange_messages"
                            label="消息整理"
                            tooltip="强制转换为user开头、user与Assistant交替格式，若重复则最取后一个"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name="parse_url_to_content"
                            label="始终解析URL"
                            tooltip="始终自动解析消息中的文件/网页链接为文本，作为请求内容"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            name="parse_url_prefix_enabled"
                            label="URL前缀解析"
                            tooltip={"开启后，仅当 content 以下方配置的前缀开头时，才解析为URL"}
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        {parseUrlPrefixEnabled &&
                            <Form.Item
                                name="parse_url_prefix"
                                label="自定义URL前缀"
                                tooltip={"当 content 以此内容开头时，才解析为URL"}
                            >
                                <Input placeholder="默认值：parse_url"/>
                            </Form.Item>
                        }
                        <Form.Item
                            name="custom_full_url_enabled"
                            label="绕过拼接URL后缀"
                            tooltip="开启后将不会自动拼接URL后缀"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            name="negative_optimization_enabled"
                            label="渠道负优化"
                            tooltip="开启后将自动延迟一段时间响应,流式会每输出一段延迟一段时间"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        {negativeOptimizationEnabled &&
                            <>
                                <Form.Item name="negative_optimization_time" label="延迟时间(毫秒)">
                                    <InputNumber min={3} max={5000}/>
                                </Form.Item>
                                <Form.Item name="negative_random_offset" label="随机偏移量(毫秒)">
                                    <InputNumber min={3} max={5000}/>
                                </Form.Item>
                            </>
                        }
                        <Form.Item
                            name="original_model_fake_resp_enabled"
                            label="伪造原始模型响应"
                            tooltip="开启后将伪造成原始模型的响应名字以及id前缀"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            name="fake_completion_id_enabled"
                            label="伪造completion_id"
                            tooltip="开启后将伪造成真实的completion_id,此选项在之前的版本中合并到[伪造原始模型响应]中,根据客户个性化诉求现在分离出来了"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            name="exclude_custom_prompt_cost_enabled"
                            label="排除自定义提示词成本"
                            tooltip="开启后自定义的提示词将不记入用户的消费"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            name="force_chat_url_enabled"
                            label="强制chatURL"
                            tooltip="开启后强制请求上游的/v1/chat/completions"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            name="claude_stream_enabled"
                            label="claude格式返回"
                            tooltip="开启后强制采用claude官方文档中的格式返回,只有请求/v1/messages才生效"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            name="transparent_proxy_enabled"
                            label="透明代理模式"
                            tooltip="暂时仅对anthropic类型渠道有效,开启后将不会对请求进行任何处理(包括模型映射也不处理)，直接转发给上游"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            name="ignore_fc_tc_enabled"
                            label="忽略FC/TC"
                            tooltip="开启后会自动去除请求中的FC/TC相关内容"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Divider orientation="right">去除广告</Divider>
                        <Form.Item
                            name="filter_stream_ad"
                            label="去除流式广告"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        {filterStreamAdEnabled &&
                            <Form.Item name="filter_stream_ad_min_size" label="最小数据块大小">
                                <InputNumber min={3} max={5000}/>
                            </Form.Item>
                        }

                        <Form.Item
                            name="filter_non_stream_ad"
                            label="去除非流式广告"
                            valuePropName="checked">
                            <Switch/>
                        </Form.Item>

                        {filterNonStreamAdEnabled &&
                            <Form.Item
                                name="filter_non_stream_ad_regex"
                                label="匹配正则表达式"
                                rules={[{pattern: /^\/.+\/$/, message: '正则表达式格式可能不正确', warningOnly: true}]}
                            >
                                <Input placeholder="过滤非流式广告的正则表达式"/>
                            </Form.Item>
                        }


                        <Divider orientation="right">其他设置</Divider>

                        <Form.Item
                            name="image_in_markdown"
                            label="Markdown图片"
                            tooltip="图片链接用Markdown格式显示（只对图片生成的模型有效）"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item name="channel_timeout_breaker_time"
                                   label="超时熔断时间(秒)"
                                   tooltip="-1为不启用,大于0为启用，单位为秒，启动后，当请求时间超过阈值时，将触发熔断"
                        >
                            <InputNumber min={-1} max={50000}/>
                        </Form.Item>
                        <Form.Item
                            name="force_o1_stream_enabled"
                            label="强制O1流式"
                            tooltip="开启后，对于不支持O1流式输出的渠道，将使用非流式请求并组装为流式数据返回"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name="image_chat_conversion_enabled"
                            label="图片聊天转换"
                            tooltip="开启后，支持将chat格式的图片请求转换为image格式，实现图片生成和编辑功能"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name="image_chat_conversion_models"
                            label="支持转换的模型"
                            tooltip="支持图片转换的模型列表，多个模型用逗号分隔，留空表示支持所有模型"
                        >
                            <Input placeholder="例如：gpt-image-1,dall-e-3"/>
                        </Form.Item>

                        <Form.Item
                            name="sync_both_db"
                            label="双数据库同步"
                            tooltip="开启后，渠道操作将同时更新SQL和NoSQL数据库，用于数据迁移和备份验证"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Divider orientation="right">渠道代理设置</Divider>

                        <Form.Item
                            name={["config", "proxy_enabled"]}
                            label="启用专属代理"
                            tooltip="开启后，该渠道的所有请求都将使用配置的代理"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name={["config", "proxy_url"]}
                            label="代理服务器URL"
                            tooltip="代理服务器地址，格式：http://host:port 或 socks5://host:port"
                        >
                            <Input placeholder="例如：http://127.0.0.1:7890 或 socks5://127.0.0.1:1080"/>
                        </Form.Item>

                        <Form.Item
                            name={["config", "proxy_auth"]}
                            label="代理认证信息"
                            tooltip="代理服务器认证信息，格式：Basic base64(username:password)"
                        >
                            <Input placeholder="例如：Basic dXNlcm5hbWU6cGFzc3dvcmQ="/>
                        </Form.Item>

                        <Divider orientation="right">图片处理配置</Divider>

                        <Form.Item
                            name={["config", "convert_base64_to_url_enabled"]}
                            label="Base64转URL"
                            tooltip="开启后，将响应中的base64图片数据转换为本地URL链接，减少传输数据量"
                            valuePropName="checked"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            name={["config", "image_server_url"]}
                            label="图片服务器地址"
                            tooltip="图片服务器的完整地址(如：http://your-server.com)，不配置则使用系统默认地址"
                        >
                            <Input placeholder="例如：http://localhost:3000 或 https://your-domain.com"/>
                        </Form.Item>

                        <Divider orientation="right">音频配置</Divider>

                        <Form.Item
                            name={["config", "audio_min_billing_seconds"]}
                            label="音频最小计费时长"
                            tooltip="音频模型的最小计费时长(秒)，默认为0表示按实际时长计费。设置后，小于此时长的音频将按最小时长计费。"
                        >
                            <InputNumber 
                                min={0} 
                                max={3600} 
                                precision={0}
                                placeholder="0"
                                addonAfter="秒"
                            />
                        </Form.Item>
                    </Form>
                </Spin>
            </Modal>

            <IdentificationUpstreamChannelModal
                isIdentificationUpstreamChannelModalOpen={isIdentificationUpstreamChannelModalOpen}
                setIdentificationUpstreamChannelModalOpen={setIdentificationUpstreamChannelModalOpen}
                FormInstance={editChannelForm}
            />

            <GenerateRequestBodyModal
                isGenerateRequestBodyModalVisible={isGenerateRequestBodyModalVisible}
                setIsGenerateRequestBodyModalVisible={setIsGenerateRequestBodyModalVisible}
                FormInstance={editChannelForm}
            />
        </>
    );

}

export default EditChannelFormModal;
